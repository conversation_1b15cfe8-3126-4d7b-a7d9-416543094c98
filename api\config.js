export default {
  debug: true,
  // 基础配置
  baseUrl: 'http://127.0.0.1:3001',
  // baseUrl: "https://manager.petsjoylife.com/api",
  // baseUrl: "https://test.xdpb.top/api",
  socketUrl: 'ws://127.0.0.1:3001',
  // socketUrl: "wss://manager.petsjoylife.com/ws/",
  timeout: 10000,
  zos_endpoint: 'xian7.zos.ctyun.cn',
  zos_bucket: 'pet',

  // API路径配置
  apiUrls: {
    // 天翼云
    zos: {
      getUploadLink: '/openapi/zos/upload-link', // 获取上传链接
      setObjHeaders: '/openapi/zos/set-object-headers', // 设置对象的 HTTP 头
      setObjAcl: '/openapi/zos/set-object-acl', // 获设置对象ACL
    },
    // 字典
    dictionary: {
      list: "/openapi/dictionary", // 查询字典列表
    },
    // 区域模拟
    area: {
      list: '/openapi/areas',
    },
    // 用户模块
    user: {
      getPhoneNumber: '/openapi/weapp/getPhoneNumber',
      login: '/openapi/user/login-for-employee',
      updateProfile: '/openapi/user/update',
      getEmployeeInfo: '/employees/{id}', // 获取员工详细信息（包括评价）
      updateEmployeeProfile: '/employee/profile', // 员工信息修改接口
      updateCustomerProfile: '/customer/profile', // 用户信息修改接口
    },
    // 消息模块
    message: {
      list: '/api/message/list/{userId}',
      detail: '/api/message/detail/{messageId}',
      markAsRead: '/api/message/mark-read/{messageId}',
      markAllAsRead: '/api/message/mark-all-read/{userId}',
      delete: '/api/message/delete/{messageId}',
      unreadCount: '/api/message/unread-count/{userId}',
    },
    // 订单模块
    order: {
      // 查询可接单列表
      list: '/orders/{userId}',
      // 按状态查询员工名下的订单列表
      myList: '/orders/employee/{employeeId}',
      // 接单
      accept: '/orders/{orderId}/accept',
      // 修改服务时间
      updateServiceTime: '/orders/{orderId}/updateServiceTime',
      // 修改服务地址
      updateServiceAddress: '/orders/{orderId}/updateServiceAddress',
      // 出发
      dispatch: '/orders/{orderId}/dispatch',
      // 开始服务
      start: '/orders/{orderId}/start',
      // 完成订单
      complete: '/orders/{orderId}/complete',
      // 服务照片相关
      uploadBeforePhotos: '/orders/{orderId}/set-before-photos',
      uploadAfterPhotos: '/orders/{orderId}/set-after-photos',
      getServicePhotos: '/orders/{orderId}/service-photos',
      // 追加服务相关
      getPendingAdditionalServices: '/employee/additional-services/pending',
      getAdditionalServices: '/order-details/{orderDetailId}/additional-services',
      confirmAdditionalService: '/order-details/{orderDetailId}/additional-services/{id}/confirm',
      rejectAdditionalService: '/order-details/{orderDetailId}/additional-services/{id}/reject',
      deleteAdditionalService: '/order-details/{orderDetailId}/additional-services/{id}',
    },
    // 支付模块
    payment: {
      // 获取追加服务支付参数
      getAdditionalServicePayParams: '/order-details/{orderDetailId}/additional-services/{id}/pay-params',
      // 确认追加服务支付
      confirmAdditionalServicePayment: '/order-details/{orderDetailId}/additional-services/{id}/pay-confirm',
      // 查询追加服务支付状态
      getAdditionalServicePayStatus: '/order-details/{orderDetailId}/additional-services/{id}/pay-status',
    },
    // 评价模块
    review: {
      // 根据订单ID获取评价详情
      getByOrderId: '/reviews/order/{orderId}',
      // 获取评价列表
      list: '/reviews',
    },
    // 投诉建议模块
    complaint: {
      create: "/customers/{customerId}/complaint", // 创建投诉建议
      list: "/customers/{customerId}/complaints", // 获取客户投诉建议列表
      detail: "/complaints/{complaintId}", // 获取投诉建议详情
      update: "/complaints/{complaintId}", // 更新投诉建议
      delete: "/complaints/{complaintId}", // 删除投诉建议
      orderComplaints: "/complaints/orders/{orderId}", // 根据订单获取投诉建议
    },
    // 员工建议模块
    employeeSuggestion: {
      list: "/employee-suggestions/{employeeId}", // 获取员工建议列表
      detail: "/employee-suggestions/{employeeId}/{id}", // 获取员工建议详情
      create: "/employee-suggestions/{employeeId}", // 创建员工建议
      update: "/employee-suggestions/{employeeId}/{id}", // 更新员工建议
      delete: "/employee-suggestions/{employeeId}/{id}", // 删除员工建议
    },
    // 员工推广模块
    employeePromotion: {
      customers: "/employee-promotion/employee/{employeeId}/customers", // 查看推广的客户列表
      statistics: "/employee-promotion/employee/{employeeId}/statistics", // 查看推广统计信息
    },
    // 员工出车拍照模块
    employeeCheckin: {
      create: "/openapi/employee-checkins", // 创建打卡记录
      list: "/openapi/employee-checkins/employee/{employeeId}", // 获取员工打卡记录列表
      todayList: "/openapi/employee-checkins/employee/{employeeId}/today", // 获取今日打卡记录
      statistics: "/openapi/employee-checkins/employee/{employeeId}/statistics", // 获取打卡统计信息
      lastCheckinTime: "/employee-checkins/employee/{employeeId}/last-checkin", // 获取最后打卡时间
      delete: "/openapi/employee-checkins/{id}", // 删除打卡记录
    },
    // 特殊情况说明模块
    specialNote: {
      create: "/openapi/order-special-notes", // 添加订单特殊情况说明
      update: "/openapi/order-special-notes/{orderId}", // 更新订单特殊情况说明
      get: "/openapi/order-special-notes/{orderId}", // 查询订单特殊情况说明
      delete: "/openapi/order-special-notes/{orderId}", // 删除订单特殊情况说明
    },
    // 位置服务模块
    location: {
      updateEmployeeLocation: "/openapi/location/updateEmployeeLocation", // 员工端上报当前位置
    },
    // 服务时长统计模块
    serviceDuration: {
      startOrderService: "/employee/service-duration/start-order-service", // 开始整体订单服务
      start: "/employee/service-duration/start", // 开始单个服务
      end: "/employee/service-duration/end", // 结束服务
      orderServiceStatus: "/employee/service-duration/order-service-status/{orderId}", // 查询订单服务状态
      records: "/employee/service-duration/records/{orderId}", // 查询订单服务时长记录
      current: "/employee/service-duration/current", // 获取当前进行中的服务
      myRecords: "/employee/service-duration/my-records", // 查询我的服务时长记录
    },
    // 车辆管理模块
    vehicle: {
      getEmployeeVehicle: "/openapi/vehicles/employee/{employeeId}", // 获取员工关联的车辆信息
      updateVehicleInfo: "/openapi/vehicles/{vehicleId}/info", // 更新车辆信息
      getVehicleDetail: "/openapi/vehicles/{vehicleId}", // 获取车辆详细信息
    },
  },
};