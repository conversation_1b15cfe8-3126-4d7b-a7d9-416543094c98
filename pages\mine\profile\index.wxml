<!-- 遮罩层 -->
<view class="modal-mask" catchtap="cancelEdit">
  <!-- 弹窗内容 -->
  <view class="modal-content" catchtap="preventTap">
    <!-- 标题栏 -->
    <view class="modal-header">
      <text class="modal-title">编辑个人信息</text>
      <text class="close-btn" bind:tap="cancelEdit">✕</text>
    </view>

    <!-- 表单内容 -->
    <view class="modal-body">
      <!-- 头像编辑 -->
      <view class="form-item">
        <text class="form-label">头像</text>
        <view class="avatar-section">
          <view class="avatar-container" bind:tap="uploadAvatar">
            <image
              src="{{formData.avatar || '//xian7.zos.ctyun.cn/pet/static/memberAvatar.png'}}"
              class="avatar-image"
              mode="aspectFill"
            />
          </view>
          <button class="avatar-btn {{uploading ? 'loading' : ''}}" bind:tap="uploadAvatar" disabled="{{uploading}}">
            {{uploading ? '上传中...' : '更换头像'}}
          </button>
        </view>
      </view>

      <!-- 昵称编辑 -->
      <view class="form-item">
        <text class="form-label">昵称</text>
        <view class="input-wrapper">
          <input
            class="form-input"
            placeholder="客户朱鹏亮"
            value="{{formData.name}}"
            data-field="name"
            bindinput="onInputChange"
            maxlength="12"
          />
          <text class="input-counter">{{formData.name.length || 0}}/12</text>
        </view>
      </view>

      <!-- 手机号显示（不可编辑） -->
      <view class="form-item">
        <text class="form-label">手机号</text>
        <view class="readonly-input">
          <text class="readonly-text">{{userInfo.phone || '18629098480'}}</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="modal-footer">
      <button
        class="btn btn-cancel"
        bind:tap="cancelEdit"
        disabled="{{saving}}"
      >
        取消
      </button>
      <button
        class="btn btn-save {{saving ? 'loading' : ''}}"
        bind:tap="saveProfile"
        disabled="{{saving || uploading}}"
      >
        {{saving ? '保存中...' : '保存'}}
      </button>
    </view>
  </view>
</view>
